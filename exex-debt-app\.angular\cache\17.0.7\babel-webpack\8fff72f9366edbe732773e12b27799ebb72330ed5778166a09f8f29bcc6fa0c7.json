{"ast": null, "code": "import { Path } from './../core/enums/path.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.layout.service\";\nimport * as i2 from \"@app/core/services/auth.service\";\nimport * as i3 from \"primeng/badge\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"primeng/image\";\nimport * as i6 from \"primeng/avatar\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"topbarmenubutton\"];\nconst _c2 = [\"topbarmenu\"];\nexport class AppTopBarComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n    this.pathUser = Path.DASHBOARD_USER;\n  }\n  signout() {\n    console.log('Signout button clicked');\n    // Try API logout first, but fallback to immediate logout if needed\n    this.authService.logout().subscribe({\n      next: response => {\n        console.log('Logout successful:', response.message);\n      },\n      error: error => {\n        console.error('Logout error, falling back to immediate logout:', error);\n        // Fallback to immediate logout if API fails\n        this.authService.logoutImmediate();\n      }\n    });\n  }\n  // Emergency logout method (can be called from console if needed)\n  forceLogout() {\n    console.log('Force logout initiated');\n    this.authService.logoutImmediate();\n  }\n  // Complete app data reset (can be called from console if needed)\n  resetAppData() {\n    console.log('App data reset initiated');\n    this.authService.clearAllAppData();\n  }\n  static #_ = this.ɵfac = function AppTopBarComponent_Factory(t) {\n    return new (t || AppTopBarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppTopBarComponent,\n    selectors: [[\"app-topbar\"]],\n    viewQuery: function AppTopBarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.topbarMenuButton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menu = _t.first);\n      }\n    },\n    decls: 9,\n    vars: 1,\n    consts: [[1, \"layout-topbar\", \"justify-content-between\"], [1, \"flex\", \"align-items-center\"], [\"src\", \"assets/images/exex.png\", \"alt\", \"Image\", \"width\", \"100\", 1, \"p-link\", 3, \"click\"], [\"menubutton\", \"\"], [\"icon\", \"pi pi-user\", \"image\", \"assets/user-avatar.png\", \"shape\", \"circle\", \"size\", \"large\", 1, \"custom-avatar\", \"mr-4\", \"p-link\", 3, \"routerLink\"], [\"pBadge\", \"\", \"badgeSize\", \"small\", \"severity\", \"danger\", \"value\", \"2\", 1, \"pi\", \"pi-bell\", \"mr-3\", \"text-2xl\", \"p-link\"], [\"title\", \"\\u0110\\u0103ng xu\\u1EA5t\", 1, \"p-link\", \"layout-topbar-button\", 3, \"click\"], [1, \"pi\", \"pi-sign-out\", \"text-2xl\"]],\n    template: function AppTopBarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"p-image\", 2, 3);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_p_image_click_2_listener() {\n          return ctx.layoutService.onMenuToggle();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\");\n        i0.ɵɵelement(5, \"p-avatar\", 4)(6, \"i\", 5);\n        i0.ɵɵelementStart(7, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_7_listener() {\n          return ctx.signout();\n        });\n        i0.ɵɵelement(8, \"i\", 7);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"routerLink\", ctx.pathUser);\n      }\n    },\n    dependencies: [i3.BadgeDirective, i4.RouterLink, i5.Image, i6.Avatar],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["Path", "AppTopBarComponent", "constructor", "layoutService", "authService", "pathUser", "DASHBOARD_USER", "signout", "console", "log", "logout", "subscribe", "next", "response", "message", "error", "logoutImmediate", "forceLogout", "resetAppData", "clearAllAppData", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "AuthService", "_2", "selectors", "viewQuery", "AppTopBarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppTopBarComponent_Template_p_image_click_2_listener", "onMenuToggle", "ɵɵelementEnd", "ɵɵelement", "AppTopBarComponent_Template_button_click_7_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.topbar.component.ts"], "sourcesContent": ["import { Path } from './../core/enums/path.enum';\r\nimport { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { LayoutService } from './app.layout.service';\r\nimport { AuthService } from '@app/core/services/auth.service';\r\n\r\n@Component({\r\n    selector: 'app-topbar',\r\n    template: `<div class=\"layout-topbar justify-content-between\">\r\n        <div class=\"flex align-items-center\">\r\n            <!-- <button #menubutton class=\"p-link layout-topbar-button\" (click)=\"layoutService.onMenuToggle()\">\r\n                <i class=\"pi pi-bars\"></i>\r\n            </button> -->\r\n            <p-image\r\n                #menubutton\r\n                src=\"assets/images/exex.png\"\r\n                alt=\"Image\"\r\n                width=\"100\"\r\n                class=\"p-link\"\r\n                (click)=\"layoutService.onMenuToggle()\" />\r\n        </div>\r\n        <div>\r\n            <p-avatar\r\n                [routerLink]=\"pathUser\"\r\n                class=\"custom-avatar mr-4 p-link\"\r\n                icon=\"pi pi-user\"\r\n                image=\"assets/user-avatar.png\"\r\n                shape=\"circle\"\r\n                size=\"large\"></p-avatar>\r\n\r\n            <i class=\"pi pi-bell mr-3 text-2xl p-link\" pBadge badgeSize=\"small\" severity=\"danger\" value=\"2\"></i>\r\n\r\n            <button class=\"p-link layout-topbar-button\" (click)=\"signout()\" title=\"Đăng xuất\">\r\n                <i class=\"pi pi-sign-out text-2xl\"></i>\r\n            </button>\r\n        </div>\r\n    </div>`,\r\n})\r\nexport class AppTopBarComponent {\r\n    pathUser = Path.DASHBOARD_USER;\r\n\r\n    items!: MenuItem[];\r\n\r\n    @ViewChild('menubutton') menuButton!: ElementRef;\r\n\r\n    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;\r\n\r\n    @ViewChild('topbarmenu') menu!: ElementRef;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService,\r\n    ) {}\r\n\r\n    signout() {\r\n        console.log('Signout button clicked');\r\n\r\n        // Try API logout first, but fallback to immediate logout if needed\r\n        this.authService.logout().subscribe({\r\n            next: (response) => {\r\n                console.log('Logout successful:', response.message);\r\n            },\r\n            error: (error) => {\r\n                console.error('Logout error, falling back to immediate logout:', error);\r\n                // Fallback to immediate logout if API fails\r\n                this.authService.logoutImmediate();\r\n            },\r\n        });\r\n    }\r\n\r\n    // Emergency logout method (can be called from console if needed)\r\n    forceLogout() {\r\n        console.log('Force logout initiated');\r\n        this.authService.logoutImmediate();\r\n    }\r\n\r\n    // Complete app data reset (can be called from console if needed)\r\n    resetAppData() {\r\n        console.log('App data reset initiated');\r\n        this.authService.clearAllAppData();\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,2BAA2B;;;;;;;;;;;AAsChD,OAAM,MAAOC,kBAAkB;EAW3BC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;IAZvB,KAAAC,QAAQ,GAAGL,IAAI,CAACM,cAAc;EAa3B;EAEHC,OAAOA,CAAA;IACHC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IAErC;IACA,IAAI,CAACL,WAAW,CAACM,MAAM,EAAE,CAACC,SAAS,CAAC;MAChCC,IAAI,EAAGC,QAAQ,IAAI;QACfL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,QAAQ,CAACC,OAAO,CAAC;MACvD,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACbP,OAAO,CAACO,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE;QACA,IAAI,CAACX,WAAW,CAACY,eAAe,EAAE;MACtC;KACH,CAAC;EACN;EAEA;EACAC,WAAWA,CAAA;IACPT,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACL,WAAW,CAACY,eAAe,EAAE;EACtC;EAEA;EACAE,YAAYA,CAAA;IACRV,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC,IAAI,CAACL,WAAW,CAACe,eAAe,EAAE;EACtC;EAAC,QAAAC,CAAA,G;qBA1CQnB,kBAAkB,EAAAoB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB1B,kBAAkB;IAAA2B,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;QA9BhBV,EAAA,CAAAY,cAAA,aAAmD;QAWlDZ,EAAA,CAAAa,UAAA,mBAAAC,qDAAA;UAAA,OAASH,GAAA,CAAA7B,aAAA,CAAAiC,YAAA,EAA4B;QAAA,EAAC;QAN1Cf,EAAA,CAAAgB,YAAA,EAM6C;QAEjDhB,EAAA,CAAAY,cAAA,UAAK;QACDZ,EAAA,CAAAiB,SAAA,kBAM4B;QAI5BjB,EAAA,CAAAY,cAAA,gBAAkF;QAAtCZ,EAAA,CAAAa,UAAA,mBAAAK,oDAAA;UAAA,OAASP,GAAA,CAAAzB,OAAA,EAAS;QAAA,EAAC;QAC3Dc,EAAA,CAAAiB,SAAA,WAAuC;QAC3CjB,EAAA,CAAAgB,YAAA,EAAS;;;QAXLhB,EAAA,CAAAmB,SAAA,GAAuB;QAAvBnB,EAAA,CAAAoB,UAAA,eAAAT,GAAA,CAAA3B,QAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}