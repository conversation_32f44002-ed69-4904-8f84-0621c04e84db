.layout-main-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
    justify-content: space-between;
    padding: 7rem 2rem 1rem 4rem;
    transition: margin-left $transitionDuration, padding 0.2s ease;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg,
            var(--nisshin-red-primary, #dc2626) 0%,
            var(--nisshin-red-accent, #f87171) 50%,
            var(--nisshin-red-primary, #dc2626) 100%);
        border-radius: 0 0 2px 2px;
    }
}

.layout-main {
    flex: 1 1 auto;
    width: 100%;
    max-width: 100%;
    padding: var(--spacing-lg, 1.5rem) 0;

    .header-custom {
        background: linear-gradient(135deg, #ffffff, #f8fafc);
        border: 1px solid var(--border-color-enhanced, #e2e8f0);
        border-radius: var(--radius-lg, 12px);
        padding: var(--spacing-lg, 1.5rem);
        margin-bottom: var(--spacing-xl, 2rem);
        box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
        transition: all 0.2s ease;

        &:hover {
            box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
            transform: translateY(-1px);
        }

        .layout-menuitem-icon {
            color: var(--nisshin-red-primary, #dc2626);
            font-size: 1.25rem;
            margin-right: var(--spacing-sm, 0.5rem);
        }

        .layout-menuitem-text {
            color: var(--text-primary-enhanced, #1e293b);
            font-size: 1.5rem;
            font-weight: 600;
            letter-spacing: -0.025em;
        }
    }

    section {
        animation: fadeInUp 0.3s ease-out;
    }
}

/* Enhanced responsive design for content */
@media (max-width: 991px) {
    .layout-main-container {
        padding: 7rem 1rem 1rem 1rem;
    }

    .layout-main {
        .header-custom {
            padding: var(--spacing-md, 1rem);
            margin-bottom: var(--spacing-lg, 1.5rem);

            .layout-menuitem-text {
                font-size: 1.25rem;
            }
        }
    }
}

@media (max-width: 768px) {
    .layout-main-container {
        padding: 6rem 0.75rem 1rem 0.75rem;
    }

    .layout-main {
        .header-custom {
            padding: var(--spacing-sm, 0.5rem) var(--spacing-md, 1rem);

            .layout-menuitem-text {
                font-size: 1.125rem;
            }
        }
    }
}
