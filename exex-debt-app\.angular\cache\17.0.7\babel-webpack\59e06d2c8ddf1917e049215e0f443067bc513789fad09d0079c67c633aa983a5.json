{"ast": null, "code": "import { Path } from '../core/enums/path.enum';\nexport const MENU_LIST = [{\n  label: 'Dashboard',\n  items: [{\n    label: 'Customer',\n    icon: 'pi pi-users',\n    routerLink: [Path.DASHBOARD_CUSTOMER]\n  }, {\n    label: 'Invoice',\n    icon: 'pi pi-file',\n    routerLink: [Path.DASHBOARD_INVOICE]\n  }]\n}];", "map": {"version": 3, "names": ["Path", "MENU_LIST", "label", "items", "icon", "routerLink", "DASHBOARD_CUSTOMER", "DASHBOARD_INVOICE"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\menu-list.ts"], "sourcesContent": ["import { Path } from '../core/enums/path.enum';\r\n\r\nexport const MENU_LIST = [\r\n    {\r\n        label: 'Dashboard',\r\n        items: [\r\n            {\r\n                label: 'Customer',\r\n                icon: 'pi pi-users',\r\n                routerLink: [Path.DASHBOARD_CUSTOMER],\r\n            },\r\n            {\r\n                label: 'Invoice',\r\n                icon: 'pi pi-file',\r\n                routerLink: [Path.DASHBOARD_INVOICE],\r\n            },\r\n        ],\r\n    },\r\n];\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,yBAAyB;AAE9C,OAAO,MAAMC,SAAS,GAAG,CACrB;EACIC,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,CACH;IACID,KAAK,EAAE,UAAU;IACjBE,IAAI,EAAE,aAAa;IACnBC,UAAU,EAAE,CAACL,IAAI,CAACM,kBAAkB;GACvC,EACD;IACIJ,KAAK,EAAE,SAAS;IAChBE,IAAI,EAAE,YAAY;IAClBC,UAAU,EAAE,CAACL,IAAI,CAACO,iBAAiB;GACtC;CAER,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}