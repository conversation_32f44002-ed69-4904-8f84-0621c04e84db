import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CustomerService, Customer, PaginationParams } from '@services/customer.service';
import { ExexCommonService } from '@services/exex-common.service';
import { IPropExexTable } from '../../common/exex-table/exex-table.model';
import { CUSTOMER_COLS } from './customer-cols';

@Component({
    selector: 'app-customer',
    templateUrl: './customer.component.html',
    styleUrl: './customer.component.scss',
})
export class CustomerComponent {
    customerDialog: boolean = false;

    customers!: any[];

    customer!: any;

    selectedCustomers!: any[] | null;

    customerForm!: FormGroup;

    dataTable!: IPropExexTable;

    constructor(
        private fb: FormBuilder,
        private customerService: CustomerService,
        private exexCommonService: ExexCommonService,
    ) {}

    ngOnInit() {
        this.dataTable = {
            ...this.dataTable,
            columns: CUSTOMER_COLS,
        };

        // Load customers with default pagination
        this.loadCustomers();

        // Example of loading customers with custom pagination
        // this.loadCustomers({ page: 1, size: 20, sortBy: 'customerName', sortDirection: 'DESC' });

        this.customerForm = this.fb.group({
            customerName: ['', Validators.required],
            phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],
            email: ['', [Validators.required, Validators.email]],
            address: [''],
            creditLimit: [null, [Validators.required, Validators.min(0)]],
            currentBalance: [null, [Validators.required, Validators.min(0)]],
        });
    }

    loadCustomers(params?: PaginationParams) {
        this.customerService.getCustomers(params).subscribe({
            next: (data: Customer[]) => {
                this.dataTable = {
                    ...this.dataTable,
                    value: data,
                };
            },
            error: (error: any) => {
                console.error('Error fetching customers:', error);
                this.exexCommonService.showToastError('Failed to load customers');
            },
        });
    }

    openNew() {
        this.customerForm.reset();
        this.customer = {};
        this.customerDialog = true;
    }

    deleteSelectedProducts() {
        this.exexCommonService.showDialogConfirm(() => {
            this.dataTable.value = this.dataTable.value.filter((val) => !this.selectedCustomers?.includes(val));
            this.selectedCustomers = null;
            this.exexCommonService.showToastSuccess('Customers Deleted');
        });
    }

    selectedRow(rows: Customer[]) {
        this.selectedCustomers = [...rows];
    }

    editProduct(customer: any) {
        this.customer = { ...customer };
        this.customerForm.patchValue({
            customerName: this.customer.customerName,
            phoneNumber: this.customer.phoneNumber,
            email: this.customer.email,
            address: this.customer.address,
            creditLimit: this.customer.creditLimit,
            currentBalance: this.customer.currentBalance,
            status: this.customer.status,
            currencyId: this.customer.currencyId,
        });
        this.customerDialog = true;
    }

    deleteProduct(customer: any) {
        this.exexCommonService.showDialogConfirm(() => {
            this.dataTable.value = this.dataTable.value.filter((val) => val.customerId !== customer.customerId);
            this.customer = {};
            this.exexCommonService.showToastSuccess('Customer Deleted');
        });
    }

    hideDialog() {
        this.customerDialog = false;
    }

    saveCustomer() {
        if (this.customerForm.valid) {
            if (this.customer.customerId) {
                this.customer = [...this.customer, this.customerForm.value];
                this.dataTable.value[this.findIndexById(this.customer.customerId)] = this.customer;
                this.exexCommonService.showToastSuccess('Customer Updated');
            } else {
                this.dataTable.value.push(this.customerForm.value);
                this.exexCommonService.showToastSuccess('Customer Updated');
            }

            this.dataTable.value = [...this.dataTable.value];
            this.customerDialog = false;
            this.customer = {};
        } else {
            this.customerForm.markAllAsTouched(); // Show validation errors
        }
    }

    findIndexById(customerId: string): number {
        let index = -1;
        for (let i = 0; i < this.dataTable.value.length; i++) {
            if (this.dataTable.value[i].customerId === customerId) {
                index = i;
                break;
            }
        }
        return index;
    }
}
