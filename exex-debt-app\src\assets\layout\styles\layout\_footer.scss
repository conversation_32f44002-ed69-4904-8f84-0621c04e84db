.layout-footer {
    transition: margin-left $transitionDuration, all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg, 1.5rem) var(--spacing-xl, 2rem);
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border-top: 2px solid var(--nisshin-red-primary, #dc2626);
    border-radius: var(--radius-lg, 12px) var(--radius-lg, 12px) 0 0;
    margin-top: var(--spacing-xl, 2rem);
    box-shadow: 0 -4px 12px rgba(220, 38, 38, 0.08);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg,
            var(--nisshin-red-primary, #dc2626) 0%,
            var(--nisshin-red-accent, #f87171) 50%,
            var(--nisshin-red-primary, #dc2626) 100%);
    }

    span {
        color: var(--text-secondary-enhanced, #64748b);
        font-size: 0.875rem;
        font-weight: 500;
        letter-spacing: 0.025em;

        &:hover {
            color: var(--nisshin-red-primary, #dc2626);
        }
    }
}
