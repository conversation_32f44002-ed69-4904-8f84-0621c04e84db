{"ast": null, "code": "import { StorageUtil } from '@app/core/utils/storage.util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/app.layout.service\";\nimport * as i2 from \"@app/core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction LoginComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nfunction LoginComponent_span_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0110\\u0103ng nh\\u1EADp\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \" \\u0110ang \\u0111\\u0103ng nh\\u1EADp... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n    this.username = '';\n    this.password = '';\n    this.errorMessage = '';\n    this.isLoading = false;\n    this.showPassword = false;\n    this.rememberMe = false;\n    // Only redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      console.log('User already authenticated, redirecting to dashboard');\n      this.authService.redirectToDashboard();\n    }\n  }\n  login() {\n    // Prevent multiple submissions\n    if (this.isLoading) {\n      console.log('Login already in progress, ignoring duplicate call');\n      return;\n    }\n    if (!this.username || !this.password) {\n      this.errorMessage = 'Vui lòng nhập đầy đủ thông tin đăng nhập';\n      return;\n    }\n    console.log('Login initiated');\n    this.isLoading = true;\n    this.errorMessage = '';\n    const credentials = {\n      username: this.username,\n      password: this.password\n    };\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('Login successful:', response);\n        console.log('Token stored:', StorageUtil.getAccessToken() ? '✅ Present' : '❌ Missing');\n        console.log('Is authenticated:', this.authService.isAuthenticated());\n        // Debug storage state\n        StorageUtil.debugStorage();\n        // Navigation is handled in the service\n      },\n\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = error.error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';\n        console.error('Login error:', error);\n      }\n    });\n  }\n  // Handle Enter key press\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !this.isLoading) {\n      this.login();\n    }\n  }\n  togglePassword() {\n    this.showPassword = !this.showPassword;\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 63,\n    vars: 15,\n    consts: [[1, \"login-container\"], [1, \"left-panel\"], [1, \"brand-section\"], [1, \"logo-section\"], [1, \"logo-placeholder\"], [1, \"logo-text\"], [1, \"logo-subtext\"], [1, \"brand-title\"], [1, \"brand-subtitle\"], [1, \"brand-description\"], [1, \"decorative-circle\", \"circle-1\"], [1, \"decorative-circle\", \"circle-2\"], [1, \"decorative-circle\", \"circle-3\"], [1, \"modules-section\"], [1, \"module-card\"], [1, \"module-title\"], [1, \"module-subtitle\"], [1, \"right-panel\"], [1, \"login-form-container\"], [1, \"login-header\"], [1, \"login-title\"], [1, \"login-subtitle\"], [1, \"login-form\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"username\", 1, \"form-label\"], [\"id\", \"username\", \"name\", \"username\", \"type\", \"text\", \"placeholder\", \"Nh\\u1EADp t\\u00EAn \\u0111\\u0103ng nh\\u1EADp c\\u1EE7a b\\u1EA1n\", \"required\", \"\", 1, \"form-input\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keypress\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"password-wrapper\"], [\"id\", \"password\", \"name\", \"password\", \"placeholder\", \"Nh\\u1EADp m\\u1EADt kh\\u1EA9u\", \"required\", \"\", 1, \"form-input\", \"password-input\", 3, \"type\", \"ngModel\", \"disabled\", \"ngModelChange\", \"keypress\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"disabled\", \"click\"], [1, \"form-options\"], [1, \"checkbox-wrapper\"], [\"type\", \"checkbox\", \"name\", \"rememberMe\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkmark\"], [\"href\", \"#\", 1, \"forgot-password\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"error-message\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5);\n        i0.ɵɵtext(6, \"NISSHIN TECHNOMIC\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"span\", 6);\n        i0.ɵɵtext(8, \"VIETNAM\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"h1\", 7);\n        i0.ɵɵtext(10, \"Debt Management System\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"h2\", 8);\n        i0.ɵɵtext(12, \"H\\u1EC7 Th\\u1ED1ng Qu\\u1EA3n L\\u00FD C\\u00F4ng N\\u1EE3 Chuy\\u00EAn Nghi\\u1EC7p\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 9)(14, \"p\");\n        i0.ɵɵtext(15, \"Gi\\u1EA3i ph\\u00E1p qu\\u1EA3n l\\u00FD c\\u00F4ng n\\u1EE3 to\\u00E0n di\\u1EC7n, theo d\\u00F5i v\\u00E0 c\\u1EA3nh b\\u00E1o c\\u00F4ng n\\u1EE3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"p\");\n        i0.ɵɵtext(17, \"gi\\u00FAp doanh nghi\\u1EC7p t\\u1ED1i \\u01B0u h\\u00F3a d\\u00F2ng ti\\u1EC1n v\\u00E0 gi\\u1EA3m thi\\u1EC3u r\\u1EE7i ro.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(18, \"div\", 10)(19, \"div\", 11)(20, \"div\", 12);\n        i0.ɵɵelementStart(21, \"div\", 13)(22, \"div\", 14)(23, \"div\", 15);\n        i0.ɵɵtext(24, \"ORDER MANAGEMENT\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 16);\n        i0.ɵɵtext(26, \"Qu\\u1EA3n l\\u00FD \\u0110\\u01A1n \\u0111\\u1EB7t h\\u00E0ng\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 14)(28, \"div\", 15);\n        i0.ɵɵtext(29, \"DEBT ALERTS\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"div\", 16);\n        i0.ɵɵtext(31, \"C\\u1EA3nh b\\u00E1o c\\u00F4ng n\\u1EE3 qu\\u00E1 h\\u1EA1n\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(32, \"div\", 17)(33, \"div\", 18)(34, \"div\", 19)(35, \"h3\", 20);\n        i0.ɵɵtext(36, \"Ch\\u00E0o m\\u1EEBng tr\\u1EDF l\\u1EA1i\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"p\", 21);\n        i0.ɵɵtext(38, \"\\u0110\\u0103ng nh\\u1EADp \\u0111\\u1EC3 truy c\\u1EADp h\\u1EC7 th\\u1ED1ng\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(39, \"form\", 22, 23);\n        i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_39_listener() {\n          return ctx.login();\n        });\n        i0.ɵɵtemplate(41, LoginComponent_div_41_Template, 2, 1, \"div\", 24);\n        i0.ɵɵelementStart(42, \"div\", 25)(43, \"label\", 26);\n        i0.ɵɵtext(44, \"T\\u00EAn \\u0111\\u0103ng nh\\u1EADp\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"input\", 27);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_45_listener($event) {\n          return ctx.username = $event;\n        })(\"keypress\", function LoginComponent_Template_input_keypress_45_listener($event) {\n          return ctx.onKeyPress($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(46, \"div\", 25)(47, \"label\", 28);\n        i0.ɵɵtext(48, \"M\\u1EADt kh\\u1EA9u\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"div\", 29)(50, \"input\", 30);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_50_listener($event) {\n          return ctx.password = $event;\n        })(\"keypress\", function LoginComponent_Template_input_keypress_50_listener($event) {\n          return ctx.onKeyPress($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"button\", 31);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_51_listener() {\n          return ctx.togglePassword();\n        });\n        i0.ɵɵelement(52, \"i\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(53, \"div\", 32)(54, \"label\", 33)(55, \"input\", 34);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_55_listener($event) {\n          return ctx.rememberMe = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(56, \"span\", 35);\n        i0.ɵɵtext(57, \" Ghi nh\\u1EDB \\u0111\\u0103ng nh\\u1EADp \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(58, \"a\", 36);\n        i0.ɵɵtext(59, \"Qu\\u00EAn m\\u1EADt kh\\u1EA9u?\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(60, \"button\", 37);\n        i0.ɵɵtemplate(61, LoginComponent_span_61_Template, 2, 0, \"span\", 38)(62, LoginComponent_span_62_Template, 3, 0, \"span\", 38);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(40);\n        i0.ɵɵadvance(41);\n        i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.username)(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\")(\"ngModel\", ctx.password)(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassMap(ctx.showPassword ? \"pi pi-eye-slash\" : \"pi pi-eye\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.rememberMe);\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading || !_r0.form.valid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      }\n    },\n    dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.CheckboxControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n    styles: [\".login-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  min-height: 100vh;\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, sans-serif;\\n}\\n\\n\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  flex: 2;\\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  padding: 3rem;\\n  color: white;\\n  overflow: hidden;\\n}\\n\\n.brand-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  max-width: 800px;\\n}\\n\\n.logo-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.logo-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  margin-bottom: 1rem;\\n}\\n\\n.logo-text[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 900;\\n  letter-spacing: 0.1em;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.logo-subtext[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  letter-spacing: 0.2em;\\n  color: rgba(255, 255, 255, 0.9);\\n  margin-top: -0.5rem;\\n}\\n\\n.brand-title[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  font-weight: 800;\\n  margin: 0 0 1rem 0;\\n  letter-spacing: -0.02em;\\n  line-height: 1;\\n  color: white;\\n}\\n\\n.brand-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin: 0 0 2rem 0;\\n  line-height: 1.3;\\n  color: white;\\n  opacity: 0.98;\\n}\\n\\n.brand-description[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  line-height: 1.6;\\n  color: white;\\n  opacity: 0.95;\\n}\\n.brand-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: white;\\n}\\n\\n\\n\\n.decorative-circle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.circle-1[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  top: 20%;\\n  right: -50px;\\n}\\n\\n.circle-2[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  top: 60%;\\n  right: 15%;\\n}\\n\\n.circle-3[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  bottom: 30%;\\n  right: 5%;\\n}\\n\\n\\n\\n.modules-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n  margin-top: 2rem;\\n}\\n\\n.module-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.15);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  padding: 1rem 1.25rem;\\n  min-width: 120px;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n}\\n.module-card[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n.module-title[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 0.875rem;\\n  margin-bottom: 0.25rem;\\n  letter-spacing: 0.5px;\\n  color: white;\\n}\\n\\n.module-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: white;\\n  opacity: 0.95;\\n  line-height: 1.2;\\n}\\n\\n\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #f8fafc;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n}\\n\\n.login-form-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 24px;\\n  padding: 3rem;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  width: 100%;\\n  max-width: 420px;\\n  position: relative;\\n}\\n.login-form-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 4px;\\n  background: #dc2626;\\n  border-radius: 0 0 4px 4px;\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.login-title[_ngcontent-%COMP%] {\\n  font-size: 1.875rem;\\n  font-weight: 700;\\n  color: #1f2937;\\n  margin: 0 0 0.5rem 0;\\n  line-height: 1.2;\\n}\\n\\n.login-subtitle[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n  margin: 0;\\n  font-weight: 500;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  background: #fef2f2;\\n  border: 1px solid #fecaca;\\n  color: #dc2626;\\n  padding: 0.75rem 1rem;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #374151;\\n  font-size: 0.875rem;\\n}\\n\\n.form-input[_ngcontent-%COMP%] {\\n  padding: 0.875rem 1rem;\\n  border: 2px solid #e5e7eb;\\n  border-radius: 12px;\\n  font-size: 1rem;\\n  transition: all 0.2s ease;\\n  background: white;\\n}\\n.form-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #dc2626;\\n  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);\\n}\\n.form-input[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n}\\n.form-input[_ngcontent-%COMP%]:disabled {\\n  background: #f9fafb;\\n  color: #6b7280;\\n  cursor: not-allowed;\\n}\\n\\n.password-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.password-input[_ngcontent-%COMP%] {\\n  padding-right: 3rem;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  color: #6b7280;\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: 4px;\\n  transition: color 0.2s ease;\\n}\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: #dc2626;\\n}\\n.password-toggle[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n  opacity: 0.5;\\n}\\n.password-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n}\\n\\n.form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin: 0.5rem 0;\\n}\\n\\n.checkbox-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  font-size: 0.875rem;\\n  color: #374151;\\n}\\n.checkbox-wrapper[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  width: 1rem;\\n  height: 1rem;\\n  accent-color: #dc2626;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  color: #dc2626;\\n  text-decoration: none;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: color 0.2s ease;\\n}\\n.forgot-password[_ngcontent-%COMP%]:hover {\\n  color: #b91c1c;\\n  text-decoration: underline;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  background: #dc2626;\\n  color: white;\\n  border: none;\\n  padding: 1rem 1.5rem;\\n  border-radius: 12px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #b91c1c;\\n  transform: translateY(-1px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n.login-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n}\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  background: #9ca3af;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n.login-button.loading[_ngcontent-%COMP%] {\\n  background: #6b7280;\\n}\\n.login-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n@media (max-width: 1024px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .left-panel[_ngcontent-%COMP%] {\\n    min-height: 40vh;\\n    padding: 2rem;\\n  }\\n  .brand-title[_ngcontent-%COMP%] {\\n    font-size: 3rem;\\n  }\\n  .brand-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .modules-section[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .right-panel[_ngcontent-%COMP%] {\\n    min-height: 60vh;\\n    padding: 1rem;\\n  }\\n  .login-form-container[_ngcontent-%COMP%] {\\n    padding: 2rem;\\n    max-width: 100%;\\n  }\\n}\\n@media (max-width: 640px) {\\n  .left-panel[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n    min-height: 35vh;\\n  }\\n  .brand-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .brand-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1.125rem;\\n  }\\n  .brand-description[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .modules-section[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .module-card[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n    min-width: 100px;\\n  }\\n  .login-form-container[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n    border-radius: 16px;\\n  }\\n  .login-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["StorageUtil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "errorMessage", "ɵɵelement", "LoginComponent", "constructor", "layoutService", "authService", "username", "password", "isLoading", "showPassword", "rememberMe", "isAuthenticated", "console", "log", "redirectToDashboard", "login", "credentials", "subscribe", "next", "response", "getAccessToken", "debugStorage", "error", "message", "onKeyPress", "event", "key", "togglePassword", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_39_listener", "ɵɵtemplate", "LoginComponent_div_41_Template", "LoginComponent_Template_input_ngModelChange_45_listener", "$event", "LoginComponent_Template_input_keypress_45_listener", "LoginComponent_Template_input_ngModelChange_50_listener", "LoginComponent_Template_input_keypress_50_listener", "LoginComponent_Template_button_click_51_listener", "LoginComponent_Template_input_ngModelChange_55_listener", "LoginComponent_span_61_Template", "LoginComponent_span_62_Template", "ɵɵproperty", "ɵɵclassMap", "ɵɵclassProp", "_r0", "form", "valid"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { AuthService } from '@app/core/services/auth.service';\r\nimport { LayoutService } from 'src/app/layout/app.layout.service';\r\nimport { LoginRequest } from '@app/core/models/auth.model';\r\nimport { StorageUtil } from '@app/core/utils/storage.util';\r\n\r\n@Component({\r\n    selector: 'app-login',\r\n    templateUrl: './login.component.html',\r\n    styleUrls: ['./login.component.scss'],\r\n})\r\nexport class LoginComponent {\r\n    username: string = '';\r\n    password: string = '';\r\n    errorMessage: string = '';\r\n    isLoading: boolean = false;\r\n    showPassword: boolean = false;\r\n    rememberMe: boolean = false;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService,\r\n    ) {\r\n        // Only redirect if already authenticated\r\n        if (this.authService.isAuthenticated()) {\r\n            console.log('User already authenticated, redirecting to dashboard');\r\n            this.authService.redirectToDashboard();\r\n        }\r\n    }\r\n\r\n    login() {\r\n        // Prevent multiple submissions\r\n        if (this.isLoading) {\r\n            console.log('Login already in progress, ignoring duplicate call');\r\n            return;\r\n        }\r\n\r\n        if (!this.username || !this.password) {\r\n            this.errorMessage = 'Vui lòng nhập đầy đủ thông tin đăng nhập';\r\n            return;\r\n        }\r\n\r\n        console.log('Login initiated');\r\n        this.isLoading = true;\r\n        this.errorMessage = '';\r\n\r\n        const credentials: LoginRequest = {\r\n            username: this.username,\r\n            password: this.password,\r\n        };\r\n\r\n        this.authService.login(credentials).subscribe({\r\n            next: (response) => {\r\n                this.isLoading = false;\r\n                console.log('Login successful:', response);\r\n                console.log('Token stored:', StorageUtil.getAccessToken() ? '✅ Present' : '❌ Missing');\r\n                console.log('Is authenticated:', this.authService.isAuthenticated());\r\n                // Debug storage state\r\n                StorageUtil.debugStorage();\r\n                // Navigation is handled in the service\r\n            },\r\n            error: (error) => {\r\n                this.isLoading = false;\r\n                this.errorMessage = error.error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';\r\n                console.error('Login error:', error);\r\n            },\r\n        });\r\n    }\r\n\r\n    // Handle Enter key press\r\n    onKeyPress(event: KeyboardEvent) {\r\n        if (event.key === 'Enter' && !this.isLoading) {\r\n            this.login();\r\n        }\r\n    }\r\n\r\n    togglePassword() {\r\n        this.showPassword = !this.showPassword;\r\n    }\r\n}\r\n", "<div class=\"login-container\">\r\n    <!-- Left Panel - Brand & Info -->\r\n    <div class=\"left-panel\">\r\n        <div class=\"brand-section\">\r\n            <div class=\"logo-section\">\r\n                <div class=\"logo-placeholder\">\r\n                    <span class=\"logo-text\">NISSHIN TECHNOMIC</span>\r\n                    <span class=\"logo-subtext\">VIETNAM</span>\r\n                </div>\r\n            </div>\r\n            <h1 class=\"brand-title\">Debt Management System</h1>\r\n            <h2 class=\"brand-subtitle\">Hệ Thống Quản Lý Công Nợ Chuyên Nghiệp</h2>\r\n\r\n            <div class=\"brand-description\">\r\n                <p>Giải pháp quản lý công nợ toàn diện, theo dõi và cảnh báo công nợ</p>\r\n                <p>giúp doanh nghiệp tối ưu hóa dòng tiền và giảm thiểu rủi ro.</p>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Decorative circles -->\r\n        <div class=\"decorative-circle circle-1\"></div>\r\n        <div class=\"decorative-circle circle-2\"></div>\r\n        <div class=\"decorative-circle circle-3\"></div>\r\n\r\n        <!-- Bottom modules -->\r\n        <div class=\"modules-section\">\r\n            <div class=\"module-card\">\r\n                <div class=\"module-title\">ORDER MANAGEMENT</div>\r\n                <div class=\"module-subtitle\">Quản lý Đơn đặt hàng</div>\r\n            </div>\r\n            <div class=\"module-card\">\r\n                <div class=\"module-title\">DEBT ALERTS</div>\r\n                <div class=\"module-subtitle\">Cảnh báo công nợ quá hạn</div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Right Panel - Login Form -->\r\n    <div class=\"right-panel\">\r\n        <div class=\"login-form-container\">\r\n            <div class=\"login-header\">\r\n                <h3 class=\"login-title\">Chào mừng trở lại</h3>\r\n                <p class=\"login-subtitle\">Đăng nhập để truy cập hệ thống</p>\r\n            </div>\r\n\r\n            <form (ngSubmit)=\"login()\" #loginForm=\"ngForm\" class=\"login-form\">\r\n                <!-- Error message display -->\r\n                <div *ngIf=\"errorMessage\" class=\"error-message\">\r\n                    {{ errorMessage }}\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                    <label for=\"username\" class=\"form-label\">Tên đăng nhập</label>\r\n                    <input\r\n                        id=\"username\"\r\n                        name=\"username\"\r\n                        type=\"text\"\r\n                        placeholder=\"Nhập tên đăng nhập của bạn\"\r\n                        [(ngModel)]=\"username\"\r\n                        class=\"form-input\"\r\n                        [disabled]=\"isLoading\"\r\n                        (keypress)=\"onKeyPress($event)\"\r\n                        required />\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                    <label for=\"password\" class=\"form-label\">Mật khẩu</label>\r\n                    <div class=\"password-wrapper\">\r\n                        <input\r\n                            id=\"password\"\r\n                            name=\"password\"\r\n                            [type]=\"showPassword ? 'text' : 'password'\"\r\n                            placeholder=\"Nhập mật khẩu\"\r\n                            [(ngModel)]=\"password\"\r\n                            class=\"form-input password-input\"\r\n                            [disabled]=\"isLoading\"\r\n                            (keypress)=\"onKeyPress($event)\"\r\n                            required />\r\n                        <button type=\"button\" class=\"password-toggle\" (click)=\"togglePassword()\" [disabled]=\"isLoading\">\r\n                            <i [class]=\"showPassword ? 'pi pi-eye-slash' : 'pi pi-eye'\"></i>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"form-options\">\r\n                    <label class=\"checkbox-wrapper\">\r\n                        <input type=\"checkbox\" [(ngModel)]=\"rememberMe\" name=\"rememberMe\" />\r\n                        <span class=\"checkmark\"></span>\r\n                        Ghi nhớ đăng nhập\r\n                    </label>\r\n                    <a href=\"#\" class=\"forgot-password\">Quên mật khẩu?</a>\r\n                </div>\r\n\r\n                <button\r\n                    type=\"submit\"\r\n                    class=\"login-button\"\r\n                    [disabled]=\"isLoading || !loginForm.form.valid\"\r\n                    [class.loading]=\"isLoading\">\r\n                    <span *ngIf=\"!isLoading\">Đăng nhập</span>\r\n                    <span *ngIf=\"isLoading\">\r\n                        <i class=\"pi pi-spin pi-spinner\"></i>\r\n                        Đang đăng nhập...\r\n                    </span>\r\n                </button>\r\n            </form>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAIA,SAASA,WAAW,QAAQ,8BAA8B;;;;;;;;IC2C1CC,EAAA,CAAAC,cAAA,cAAgD;IAC5CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,YAAA,MACJ;;;;;IAiDIP,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,+BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzCH,EAAA,CAAAC,cAAA,WAAwB;IACpBD,EAAA,CAAAQ,SAAA,YAAqC;IACrCR,EAAA,CAAAE,MAAA,8CACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD3F3B,OAAM,MAAOM,cAAc;EAQvBC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;IATvB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAP,YAAY,GAAW,EAAE;IACzB,KAAAQ,SAAS,GAAY,KAAK;IAC1B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,UAAU,GAAY,KAAK;IAMvB;IACA,IAAI,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACpCC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE,IAAI,CAACR,WAAW,CAACS,mBAAmB,EAAE;;EAE9C;EAEAC,KAAKA,CAAA;IACD;IACA,IAAI,IAAI,CAACP,SAAS,EAAE;MAChBI,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE;;IAGJ,IAAI,CAAC,IAAI,CAACP,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAClC,IAAI,CAACP,YAAY,GAAG,0CAA0C;MAC9D;;IAGJY,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,YAAY,GAAG,EAAE;IAEtB,MAAMgB,WAAW,GAAiB;MAC9BV,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA;KAClB;IAED,IAAI,CAACF,WAAW,CAACU,KAAK,CAACC,WAAW,CAAC,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGC,QAAQ,IAAI;QACf,IAAI,CAACX,SAAS,GAAG,KAAK;QACtBI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEM,QAAQ,CAAC;QAC1CP,OAAO,CAACC,GAAG,CAAC,eAAe,EAAErB,WAAW,CAAC4B,cAAc,EAAE,GAAG,WAAW,GAAG,WAAW,CAAC;QACtFR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACR,WAAW,CAACM,eAAe,EAAE,CAAC;QACpE;QACAnB,WAAW,CAAC6B,YAAY,EAAE;QAC1B;MACJ,CAAC;;MACDC,KAAK,EAAGA,KAAK,IAAI;QACb,IAAI,CAACd,SAAS,GAAG,KAAK;QACtB,IAAI,CAACR,YAAY,GAAGsB,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,uCAAuC;QACnFX,OAAO,CAACU,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACxC;KACH,CAAC;EACN;EAEA;EACAE,UAAUA,CAACC,KAAoB;IAC3B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAClB,SAAS,EAAE;MAC1C,IAAI,CAACO,KAAK,EAAE;;EAEpB;EAEAY,cAAcA,CAAA;IACV,IAAI,CAAClB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EAC1C;EAAC,QAAAmB,CAAA,G;qBAnEQ1B,cAAc,EAAAT,EAAA,CAAAoC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAtC,EAAA,CAAAoC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdhC,cAAc;IAAAiC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCX3BhD,EAAA,CAAAC,cAAA,aAA6B;QAMeD,EAAA,CAAAE,MAAA,wBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAChDH,EAAA,CAAAC,cAAA,cAA2B;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGjDH,EAAA,CAAAC,cAAA,YAAwB;QAAAD,EAAA,CAAAE,MAAA,8BAAsB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACnDH,EAAA,CAAAC,cAAA,aAA2B;QAAAD,EAAA,CAAAE,MAAA,sFAAsC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEtEH,EAAA,CAAAC,cAAA,cAA+B;QACxBD,EAAA,CAAAE,MAAA,+IAAiE;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACxEH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,2HAA4D;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAK3EH,EAAA,CAAAQ,SAAA,eAA8C;QAK9CR,EAAA,CAAAC,cAAA,eAA6B;QAEKD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAChDH,EAAA,CAAAC,cAAA,eAA6B;QAAAD,EAAA,CAAAE,MAAA,+DAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAE3DH,EAAA,CAAAC,cAAA,eAAyB;QACKD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC3CH,EAAA,CAAAC,cAAA,eAA6B;QAAAD,EAAA,CAAAE,MAAA,8DAAwB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAMvEH,EAAA,CAAAC,cAAA,eAAyB;QAGWD,EAAA,CAAAE,MAAA,6CAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC9CH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAE,MAAA,8EAA8B;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGhEH,EAAA,CAAAC,cAAA,oBAAkE;QAA5DD,EAAA,CAAAkD,UAAA,sBAAAC,kDAAA;UAAA,OAAYF,GAAA,CAAA3B,KAAA,EAAO;QAAA,EAAC;QAEtBtB,EAAA,CAAAoD,UAAA,KAAAC,8BAAA,kBAEM;QAENrD,EAAA,CAAAC,cAAA,eAAwB;QACqBD,EAAA,CAAAE,MAAA,yCAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC9DH,EAAA,CAAAC,cAAA,iBASe;QAJXD,EAAA,CAAAkD,UAAA,2BAAAI,wDAAAC,MAAA;UAAA,OAAAN,GAAA,CAAApC,QAAA,GAAA0C,MAAA;QAAA,EAAsB,sBAAAC,mDAAAD,MAAA;UAAA,OAGVN,GAAA,CAAAlB,UAAA,CAAAwB,MAAA,CAAkB;QAAA,EAHR;QAL1BvD,EAAA,CAAAG,YAAA,EASe;QAGnBH,EAAA,CAAAC,cAAA,eAAwB;QACqBD,EAAA,CAAAE,MAAA,0BAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACzDH,EAAA,CAAAC,cAAA,eAA8B;QAMtBD,EAAA,CAAAkD,UAAA,2BAAAO,wDAAAF,MAAA;UAAA,OAAAN,GAAA,CAAAnC,QAAA,GAAAyC,MAAA;QAAA,EAAsB,sBAAAG,mDAAAH,MAAA;UAAA,OAGVN,GAAA,CAAAlB,UAAA,CAAAwB,MAAA,CAAkB;QAAA,EAHR;QAL1BvD,EAAA,CAAAG,YAAA,EASe;QACfH,EAAA,CAAAC,cAAA,kBAAgG;QAAlDD,EAAA,CAAAkD,UAAA,mBAAAS,iDAAA;UAAA,OAASV,GAAA,CAAAf,cAAA,EAAgB;QAAA,EAAC;QACpElC,EAAA,CAAAQ,SAAA,SAAgE;QACpER,EAAA,CAAAG,YAAA,EAAS;QAIjBH,EAAA,CAAAC,cAAA,eAA0B;QAEKD,EAAA,CAAAkD,UAAA,2BAAAU,wDAAAL,MAAA;UAAA,OAAAN,GAAA,CAAAhC,UAAA,GAAAsC,MAAA;QAAA,EAAwB;QAA/CvD,EAAA,CAAAG,YAAA,EAAoE;QACpEH,EAAA,CAAAQ,SAAA,gBAA+B;QAC/BR,EAAA,CAAAE,MAAA,+CACJ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAC,cAAA,aAAoC;QAAAD,EAAA,CAAAE,MAAA,qCAAc;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAG1DH,EAAA,CAAAC,cAAA,kBAIgC;QAC5BD,EAAA,CAAAoD,UAAA,KAAAS,+BAAA,mBAAyC,KAAAC,+BAAA;QAK7C9D,EAAA,CAAAG,YAAA,EAAS;;;;QAxDHH,EAAA,CAAAI,SAAA,IAAkB;QAAlBJ,EAAA,CAAA+D,UAAA,SAAAd,GAAA,CAAA1C,YAAA,CAAkB;QAWhBP,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAA+D,UAAA,YAAAd,GAAA,CAAApC,QAAA,CAAsB,aAAAoC,GAAA,CAAAlC,SAAA;QAalBf,EAAA,CAAAI,SAAA,GAA2C;QAA3CJ,EAAA,CAAA+D,UAAA,SAAAd,GAAA,CAAAjC,YAAA,uBAA2C,YAAAiC,GAAA,CAAAnC,QAAA,cAAAmC,GAAA,CAAAlC,SAAA;QAO0Bf,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAA+D,UAAA,aAAAd,GAAA,CAAAlC,SAAA,CAAsB;QACxFf,EAAA,CAAAI,SAAA,GAAwD;QAAxDJ,EAAA,CAAAgE,UAAA,CAAAf,GAAA,CAAAjC,YAAA,mCAAwD;QAOxChB,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAA+D,UAAA,YAAAd,GAAA,CAAAhC,UAAA,CAAwB;QAWnDjB,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAAiE,WAAA,YAAAhB,GAAA,CAAAlC,SAAA,CAA2B;QAD3Bf,EAAA,CAAA+D,UAAA,aAAAd,GAAA,CAAAlC,SAAA,KAAAmD,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAA+C;QAExCpE,EAAA,CAAAI,SAAA,GAAgB;QAAhBJ,EAAA,CAAA+D,UAAA,UAAAd,GAAA,CAAAlC,SAAA,CAAgB;QAChBf,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAA+D,UAAA,SAAAd,GAAA,CAAAlC,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}