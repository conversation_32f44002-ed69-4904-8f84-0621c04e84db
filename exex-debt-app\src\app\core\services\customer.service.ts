import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

// Interface for individual customer data
export interface Customer {
    customerId: number;
    customerCode: string;
    customerName: string;
    address: string;
    phone: string;
    email: string;
    saler: string;
    creditLimit: number;
    creditBalance: number;
    creditDate: string;
    createdBy: number;
    createdDate: string;
    updatedBy: number;
    updatedDate: string;
}

// Interface for pagination data
export interface Pagination {
    currentPage: number;
    rowPerPage: number;
    totalPage: number;
    totalRow: number;
    sortBy: string;
    sortDirection: string;
}

// Interface for the data section of API response
export interface CustomerData {
    content: Customer[];
    pagination: Pagination;
}

// Interface for pagination parameters
export interface PaginationParams {
    page?: number;
    size?: number;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
}

// Interface for the complete API response
export interface CustomerApiResponse {
    success: boolean;
    message: string;
    data: CustomerData;
}

@Injectable({
    providedIn: 'root',
})
export class CustomerService {
    constructor(private http: HttpClient) {}

    getCustomerData() {
        const mockData = Array.from({ length: 11 }).map((_, i) => {
            return {
                id: i,
                customerId: 'COD' + i,
                customerName: 'Customer' + i,
                phoneNumber: '039876549' + i,
                email: 'nguyenvan' + i + '@gmail.com',
                address: 'HCM',
                status: 'Active',
                creditLimit: 1000000,
                currentBalance: 4900 + i,
                currencyId: 'Cur' + i,
            };
        });

        return mockData;
    }

    getInvoiceData() {
        const mockData = Array.from({ length: 11 }).map((_, i) => {
            return {
                id: i,
                invoiceId: 'Inv' + i,
                customerId: 'Customer' + i,
                invoiceDate: '02/02/2025',
                dueDate: '02/02/2025',
                totalAmount: 4900 + i,
                paidAmount: 2900 + i,
                remainingAmount: 1000000,
                status: 'Active',
                currencyId: 'Cur' + i,
            };
        });

        return mockData;
    }

    getCustomers(params?: PaginationParams): Observable<Customer[]> {
        const defaultParams = {
            page: 1,
            size: 10,
            sortBy: 'customerCode',
            sortDirection: 'ASC' as const,
        };

        const queryParams = { ...defaultParams, ...params };

        return this.http.get<CustomerApiResponse>('api/customers', { params: queryParams }).pipe(
            map((response) => {
                if (response.success && response.data && response.data.content) {
                    return response.data.content;
                } else {
                    throw new Error(response.message || 'Failed to fetch customers');
                }
            }),
        );
    }

    // Method to get customers with pagination info
    getCustomersWithPagination(params?: PaginationParams): Observable<CustomerData> {
        const defaultParams = {
            page: 1,
            size: 10,
            sortBy: 'customerCode',
            sortDirection: 'ASC' as const,
        };

        const queryParams = { ...defaultParams, ...params };

        return this.http.get<CustomerApiResponse>('api/customers', { params: queryParams }).pipe(
            map((response) => {
                if (response.success && response.data) {
                    return response.data;
                } else {
                    throw new Error(response.message || 'Failed to fetch customers');
                }
            }),
        );
    }

    getInvoices() {
        return Promise.resolve(this.getInvoiceData());
    }
}
